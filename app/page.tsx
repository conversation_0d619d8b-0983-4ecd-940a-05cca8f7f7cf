'use client'

import { Authenticated, Unauthenticated } from 'convex/react'
import { SignInButton, UserButton } from '@clerk/nextjs'
import TodoPage from './todos'

export default function Home() {
  return (
    <>
      <Authenticated>
        <div className="min-h-screen bg-gray-50">
          <div className="flex justify-between items-center p-4 bg-white shadow-sm">
            <h1 className="text-xl font-semibold">AI Todo App</h1>
            <UserButton />
          </div>
          <TodoPage />
        </div>
      </Authenticated>
      <Unauthenticated>
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <h1 className="text-3xl font-bold mb-4">AI Todo App</h1>
            <p className="text-gray-600 mb-6">Create todos using natural language</p>
            <SignInButton />
          </div>
        </div>
      </Unauthenticated>
    </>
  )
}
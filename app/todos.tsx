"use client";

import { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { parseTodoItem, expandRecurringEvents } from "@/lib/aiParser";
import { useUser } from "@clerk/nextjs";

export default function TodoPage() {
  const [inputText, setInputText] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [parsedItems, setParsedItems] = useState<any[]>([]);
  const addTodo = useMutation(api.todos.addTodo);
  const updateTodo = useMutation(api.todos.updateTodo);
  const { user } = useUser();

  const todos = useQuery(
    api.todos.getTodos,
    user ? { userId: user.id } : "skip"
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputText.trim() || !user) return;

    setIsProcessing(true);
    try {
      const parsedItem = await parseTodoItem(inputText);
      if (parsedItem) {
        const items = expandRecurringEvents(parsedItem);
        setParsedItems(items);
      }
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSave = async (item: any) => {
    if (!user) return;

    await addTodo({
      userId: user.id,
      originalText: inputText,
      title: item.title,
      date: item.date,
      startTime: item.startTime,
      endTime: item.endTime,
      duration: item.duration,
      location: item.location,
      isCompleted: false,
    });

    setInputText("");
    setParsedItems([]);
  };

  const handleToggleComplete = async (todoId: any, isCompleted: boolean) => {
    await updateTodo({
      id: todoId,
      isCompleted: !isCompleted,
    });
  };

  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Input Section */}
        <div>
          <h2 className="text-xl font-bold mb-4">Add New Todo</h2>

          <form onSubmit={handleSubmit} className="mb-6">
            <div className="flex gap-2">
              <input
                type="text"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder="Enter todo in natural language (e.g., 'Meeting Monday at 2pm')"
                className="flex-1 p-3 border rounded-lg"
                disabled={isProcessing}
              />
              <button
                type="submit"
                className="bg-blue-500 text-white px-6 py-3 rounded-lg disabled:opacity-50 hover:bg-blue-600"
                disabled={isProcessing || !inputText.trim()}
              >
                {isProcessing ? "Processing..." : "Parse"}
              </button>
            </div>
          </form>

          {parsedItems.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Parsed Items</h3>
              {parsedItems.map((item, index) => (
                <div key={index} className="border p-4 rounded-lg bg-blue-50">
                  <h4 className="font-bold text-lg">{item.title}</h4>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    <div>
                      <span className="text-sm text-gray-500">Date:</span>
                      <p className="font-medium">{item.date}</p>
                    </div>
                    {item.startTime && (
                      <div>
                        <span className="text-sm text-gray-500">Start Time:</span>
                        <p className="font-medium">{item.startTime}</p>
                      </div>
                    )}
                    {item.endTime && (
                      <div>
                        <span className="text-sm text-gray-500">End Time:</span>
                        <p className="font-medium">{item.endTime}</p>
                      </div>
                    )}
                    {item.duration && (
                      <div>
                        <span className="text-sm text-gray-500">Duration:</span>
                        <p className="font-medium">{item.duration}</p>
                      </div>
                    )}
                    {item.location && (
                      <div className="col-span-2">
                        <span className="text-sm text-gray-500">Location:</span>
                        <p className="font-medium">{item.location}</p>
                      </div>
                    )}
                  </div>
                  <button
                    onClick={() => handleSave(item)}
                    className="mt-3 bg-green-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-green-600"
                  >
                    Save Todo
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Existing Todos Section */}
        <div>
          <h2 className="text-xl font-bold mb-4">Your Todos</h2>
          {todos === undefined ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-2 text-gray-500">Loading todos...</p>
            </div>
          ) : todos.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No todos yet. Create your first one!</p>
            </div>
          ) : (
            <div className="space-y-3">
              {todos.map((todo) => (
                <div
                  key={todo._id}
                  className={`border p-4 rounded-lg ${
                    todo.isCompleted ? 'bg-gray-50 opacity-75' : 'bg-white'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className={`font-semibold ${todo.isCompleted ? 'line-through text-gray-500' : ''}`}>
                        {todo.title}
                      </h4>
                      <div className="grid grid-cols-2 gap-2 mt-2 text-sm">
                        <div>
                          <span className="text-gray-500">Date:</span>
                          <p className="font-medium">{todo.date}</p>
                        </div>
                        {todo.startTime && (
                          <div>
                            <span className="text-gray-500">Time:</span>
                            <p className="font-medium">
                              {todo.startTime}
                              {todo.endTime && ` - ${todo.endTime}`}
                            </p>
                          </div>
                        )}
                        {todo.duration && (
                          <div>
                            <span className="text-gray-500">Duration:</span>
                            <p className="font-medium">{todo.duration}</p>
                          </div>
                        )}
                        {todo.location && (
                          <div className="col-span-2">
                            <span className="text-gray-500">Location:</span>
                            <p className="font-medium">{todo.location}</p>
                          </div>
                        )}
                      </div>
                      <div className="mt-2 text-xs text-gray-400">
                        Original: "{todo.originalText}"
                      </div>
                    </div>
                    <button
                      onClick={() => handleToggleComplete(todo._id, todo.isCompleted)}
                      className={`ml-4 px-3 py-1 rounded text-sm ${
                        todo.isCompleted
                          ? 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                          : 'bg-green-500 text-white hover:bg-green-600'
                      }`}
                    >
                      {todo.isCompleted ? 'Undo' : 'Complete'}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
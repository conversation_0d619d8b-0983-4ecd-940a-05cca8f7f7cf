import { GoogleGenAI, Type } from "@google/genai";
import { NextRequest, NextResponse } from "next/server";

const ai = new GoogleGenAI({
  apiKey: process.env.GOOGLE_GENAI_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    const { text } = await request.json();

    if (!text) {
      return NextResponse.json(
        { error: "Text is required" },
        { status: 400 }
      );
    }

    const currentDate = new Date();
    const currentDateStr = currentDate.toISOString().split('T')[0];
    const currentDay = currentDate.toLocaleDateString('en-US', { weekday: 'long' });
    
    const response = await ai.models.generateContent({
      model: "gemini-2.5-flash",
      contents: `Parse the following todo item into structured data. Today is ${currentDateStr} (${currentDay}). 
      
      For dates:
      - Convert relative dates like "Monday", "next Monday", "Tuesday" to actual dates in YYYY-MM-DD format
      - If just a day is mentioned (like "Monday"), assume it's the next occurrence of that day
      - If "next [day]" is mentioned, find the next occurrence after this week
      
      For times:
      - Convert to 24-hour format (e.g., "10pm" becomes "22:00", "6pm" becomes "18:00")
      - Calculate end time if duration is given
      
      For recurring events:
      - If multiple days are mentioned (like "Tuesday and Thursday"), mark as recurring
      
      Text to parse: "${text}"`,
      config: {
        responseMimeType: "application/json",
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            title: {
              type: Type.STRING,
              description: "The main title or description of the task",
            },
            date: {
              type: Type.STRING,
              description: "The date in format 'YYYY-MM-DD'",
            },
            startTime: {
              type: Type.STRING,
              description: "Start time in 24-hour format 'HH:MM' if available",
              optional: true,
            },
            endTime: {
              type: Type.STRING,
              description: "End time in 24-hour format 'HH:MM' if available",
              optional: true,
            },
            duration: {
              type: Type.STRING,
              description: "Duration in hours/minutes if available",
              optional: true,
            },
            location: {
              type: Type.STRING,
              description: "Location if mentioned",
              optional: true,
            },
            isRecurring: {
              type: Type.BOOLEAN,
              description: "Whether the event repeats",
              optional: true,
            },
            recurrencePattern: {
              type: Type.STRING,
              description: "Recurrence pattern if applicable (e.g., 'every Tuesday and Thursday')",
              optional: true,
            },
          },
        },
      },
    });

    if (!response.text) {
      throw new Error("No response text received from AI");
    }

    const parsedItem = JSON.parse(response.text);
    
    return NextResponse.json({ parsedItem });
  } catch (error) {
    console.error("Error parsing todo item:", error);
    return NextResponse.json(
      { error: "Failed to parse todo item" },
      { status: 500 }
    );
  }
}

import { GoogleGenAI, Type } from "@google/genai";

const ai = new GoogleGenAI({
  // Configure with your API key (store this securely, preferably in environment variables)
  apiKey: process.env.GOOGLE_GENAI_API_KEY,
});

export async function parseTodoItem(naturalLanguageText: string) {
  try {
    const response = await ai.models.generateContent({
      model: "gemini-2.5-flash",
      contents: `Parse the following todo item into structured data: "${naturalLanguageText}"`,
      config: {
        responseMimeType: "application/json",
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            title: {
              type: Type.STRING,
              description: "The main title or description of the task",
            },
            date: {
              type: Type.STRING,
              description: "The date in format 'YYYY-MM-DD' or relative date like 'Next Monday'",
            },
            startTime: {
              type: Type.STRING,
              description: "Start time in 24-hour format 'HH:MM' if available",
              optional: true,
            },
            endTime: {
              type: Type.STRING,
              description: "End time in 24-hour format 'HH:MM' if available",
              optional: true,
            },
            duration: {
              type: Type.STRING,
              description: "Duration in hours/minutes if available",
              optional: true,
            },
            location: {
              type: Type.STRING,
              description: "Location if mentioned",
              optional: true,
            },
            isRecurring: {
              type: Type.BOOLEAN,
              description: "Whether the event repeats",
              optional: true,
            },
            recurrencePattern: {
              type: Type.STRING,
              description: "Recurrence pattern if applicable (e.g., 'every Tuesday and Thursday')",
              optional: true,
            },
          },
        },
      },
    });

    return JSON.parse(response.text);
  } catch (error) {
    console.error("Error parsing todo item:", error);
    return null;
  }
}

// Helper function to expand recurring events
export function expandRecurringEvents(parsedItem: any, count: number = 2) {
  if (!parsedItem.isRecurring || !parsedItem.recurrencePattern) {
    return [parsedItem];
  }

  // This is a simplified version - you might want to use a proper recurrence library
  // like rrule or implement more sophisticated logic
  const events = [];
  for (let i = 0; i < count; i++) {
    const event = { ...parsedItem };
    // This is placeholder logic - implement proper date calculation based on pattern
    event.date = `Next ${i + 1} occurrence`; 
    events.push(event);
  }
  return events;
}
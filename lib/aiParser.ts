import { GoogleGenAI, Type } from "@google/genai";

const ai = new GoogleGenAI({
  // Configure with your API key (store this securely, preferably in environment variables)
  apiKey: process.env.GOOGLE_GENAI_API_KEY,
});

export async function parseTodoItem(naturalLanguageText: string) {
  try {
    const currentDate = new Date();
    const currentDateStr = currentDate.toISOString().split('T')[0];
    const currentDay = currentDate.toLocaleDateString('en-US', { weekday: 'long' });

    const response = await ai.models.generateContent({
      model: "gemini-2.5-flash",
      contents: `Parse the following todo item into structured data. Today is ${currentDateStr} (${currentDay}).

      For dates:
      - Convert relative dates like "Monday", "next Monday", "Tuesday" to actual dates in YYYY-MM-DD format
      - If just a day is mentioned (like "Monday"), assume it's the next occurrence of that day
      - If "next [day]" is mentioned, find the next occurrence after this week

      For times:
      - Convert to 24-hour format (e.g., "10pm" becomes "22:00", "6pm" becomes "18:00")
      - Calculate end time if duration is given

      For recurring events:
      - If multiple days are mentioned (like "Tuesday and Thursday"), mark as recurring

      Text to parse: "${naturalLanguageText}"`,
      config: {
        responseMimeType: "application/json",
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            title: {
              type: Type.STRING,
              description: "The main title or description of the task",
            },
            date: {
              type: Type.STRING,
              description: "The date in format 'YYYY-MM-DD'",
            },
            startTime: {
              type: Type.STRING,
              description: "Start time in 24-hour format 'HH:MM' if available",
              optional: true,
            },
            endTime: {
              type: Type.STRING,
              description: "End time in 24-hour format 'HH:MM' if available",
              optional: true,
            },
            duration: {
              type: Type.STRING,
              description: "Duration in hours/minutes if available",
              optional: true,
            },
            location: {
              type: Type.STRING,
              description: "Location if mentioned",
              optional: true,
            },
            isRecurring: {
              type: Type.BOOLEAN,
              description: "Whether the event repeats",
              optional: true,
            },
            recurrencePattern: {
              type: Type.STRING,
              description: "Recurrence pattern if applicable (e.g., 'every Tuesday and Thursday')",
              optional: true,
            },
          },
        },
      },
    });

    if (!response.text) {
      throw new Error("No response text received from AI");
    }

    return JSON.parse(response.text);
  } catch (error) {
    console.error("Error parsing todo item:", error);
    return null;
  }
}

// Helper function to expand recurring events
export function expandRecurringEvents(parsedItem: any, count: number = 4) {
  if (!parsedItem.isRecurring || !parsedItem.recurrencePattern) {
    return [parsedItem];
  }

  const events = [];
  const baseDate = new Date(parsedItem.date);

  // Handle "Tuesday and Thursday" pattern
  if (parsedItem.recurrencePattern.toLowerCase().includes('tuesday') &&
      parsedItem.recurrencePattern.toLowerCase().includes('thursday')) {

    // Find next Tuesday and Thursday occurrences
    const daysOfWeek = [2, 4]; // Tuesday = 2, Thursday = 4
    let currentDate = new Date(baseDate);

    for (let i = 0; i < count; i++) {
      const targetDay = daysOfWeek[i % 2];

      // Find next occurrence of target day
      while (currentDate.getDay() !== targetDay) {
        currentDate.setDate(currentDate.getDate() + 1);
      }

      const event = {
        ...parsedItem,
        date: currentDate.toISOString().split('T')[0],
        isRecurring: false // Individual instances are not recurring
      };
      events.push(event);

      // Move to next day to find next occurrence
      currentDate.setDate(currentDate.getDate() + 1);
    }
  } else {
    // For other patterns, create weekly recurrence
    for (let i = 0; i < count; i++) {
      const eventDate = new Date(baseDate);
      eventDate.setDate(eventDate.getDate() + (i * 7));

      const event = {
        ...parsedItem,
        date: eventDate.toISOString().split('T')[0],
        isRecurring: false // Individual instances are not recurring
      };
      events.push(event);
    }
  }

  return events;
}
import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const addTodo = mutation({
  args: {
    userId: v.string(),
    originalText: v.string(),
    title: v.string(),
    date: v.string(),
    startTime: v.optional(v.string()),
    endTime: v.optional(v.string()),
    duration: v.optional(v.string()),
    location: v.optional(v.string()),
    isCompleted: v.boolean(),
  },
  handler: async (ctx, args) => {
    await ctx.db.insert("todos", args);
  },
});

export const getTodos = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("todos")
      .filter((q) => q.eq(q.field("userId"), args.userId))
      .order("desc")
      .collect();
  },
});

export const updateTodo = mutation({
  args: {
    id: v.id("todos"),
    isCompleted: v.boolean(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.id, { isCompleted: args.isCompleted });
  },
});
// Test script to verify AI parser functionality
import { parseTodoItem, expandRecurringEvents } from './lib/aiParser.js';

async function testParser() {
  console.log('Testing AI Parser...\n');
  
  const testCases = [
    "Monday meeting at 10pm with boss",
    "Yoga Tuesday and Thursday at 6pm for 2 hours at fitness center",
    "Call mom by 3pm"
  ];
  
  for (const testCase of testCases) {
    console.log(`\n--- Testing: "${testCase}" ---`);
    
    try {
      const parsed = await parseTodoItem(testCase);
      console.log('Parsed result:', JSON.stringify(parsed, null, 2));
      
      if (parsed) {
        const expanded = expandRecurringEvents(parsed);
        console.log('Expanded events:', expanded.length);
        expanded.forEach((event, index) => {
          console.log(`  ${index + 1}. ${event.title} - ${event.date} at ${event.startTime || 'no time'}`);
        });
      }
    } catch (error) {
      console.error('Error:', error.message);
    }
  }
}

// Run the test
testParser().catch(console.error);
